using FarmerManager.Application.Common.Models;
using FarmerManager.Application.Contracts.Repositories;
using FarmerManager.Application.DTOs.Farmer;
using FarmerManager.Domain.Entities;
using FarmerManager.Persistence.Data;
using Microsoft.EntityFrameworkCore;

namespace FarmerManager.Persistence.Repositories;

public class FarmerRepository : IFarmerRepository
{
    private readonly FarmerManagerDbContext _context;

    public FarmerRepository(FarmerManagerDbContext context)
    {
        _context = context;
    }

    public async Task<PagedResult<Farmer>> GetAllAsync(SearchFarmerDto searchDto, CancellationToken cancellationToken = default)
    {
        var query = _context.Farmers.AsQueryable();

        // Apply filters
        if (!string.IsNullOrWhiteSpace(searchDto.FarmerCode))
        {
            query = query.Where(f => f.FarmerCode.Contains(searchDto.FarmerCode));
        }

        if (!string.IsNullOrWhiteSpace(searchDto.FarmerName))
        {
            query = query.Where(f => f.FarmerName.Contains(searchDto.FarmerName));
        }

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination
        var items = await query
            .OrderBy(f => f.FarmerCode)
            .Skip((searchDto.PageNumber - 1) * searchDto.PageSize)
            .Take(searchDto.PageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<Farmer>(items, totalCount, searchDto.PageNumber, searchDto.PageSize);
    }

    public async Task<Farmer?> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _context.Farmers.FindAsync(new object[] { id }, cancellationToken);
    }

    public async Task<Farmer?> GetByCodeAsync(string farmerCode, CancellationToken cancellationToken = default)
    {
        return await _context.Farmers
            .FirstOrDefaultAsync(f => f.FarmerCode == farmerCode, cancellationToken);
    }

    public async Task<bool> ExistsByCodeAsync(string farmerCode, CancellationToken cancellationToken = default)
    {
        return await _context.Farmers
            .AnyAsync(f => f.FarmerCode == farmerCode, cancellationToken);
    }

    public async Task<bool> ExistsByCodeAsync(string farmerCode, int excludeId, CancellationToken cancellationToken = default)
    {
        return await _context.Farmers
            .AnyAsync(f => f.FarmerCode == farmerCode && f.Id != excludeId, cancellationToken);
    }

    public async Task<Farmer> CreateAsync(Farmer farmer, CancellationToken cancellationToken = default)
    {
        _context.Farmers.Add(farmer);
        await _context.SaveChangesAsync(cancellationToken);
        return farmer;
    }

    public async Task<Farmer> UpdateAsync(Farmer farmer, CancellationToken cancellationToken = default)
    {
        _context.Farmers.Update(farmer);
        await _context.SaveChangesAsync(cancellationToken);
        return farmer;
    }

    public async Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default)
    {
        var farmer = await GetByIdAsync(id, cancellationToken);
        if (farmer == null)
        {
            return false;
        }

        _context.Farmers.Remove(farmer);
        await _context.SaveChangesAsync(cancellationToken);
        return true;
    }
}
