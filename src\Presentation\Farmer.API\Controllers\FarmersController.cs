using FarmerManager.Application.Common.Models;
using FarmerManager.Application.DTOs.Farmer;
using FarmerManager.Application.Features.Farmers.Commands.CreateFarmer;
using FarmerManager.Application.Features.Farmers.Commands.DeleteFarmer;
using FarmerManager.Application.Features.Farmers.Commands.UpdateFarmer;
using FarmerManager.Application.Features.Farmers.Queries.GetAllFarmers;
using FarmerManager.Application.Features.Farmers.Queries.GetFarmerById;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Farmer.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class FarmersController : ControllerBase
{
    private readonly IMediator _mediator;

    public FarmersController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// L<PERSON>y danh sách tất cả farmers với tìm kiếm và phân trang
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<PagedResult<FarmerDto>>>> GetFarmers([FromQuery] SearchFarmerDto searchDto)
    {
        var query = new GetAllFarmersQuery(searchDto);
        var result = await _mediator.Send(query);
        
        if (result.Success)
            return Ok(result);
        
        return BadRequest(result);
    }

    /// <summary>
    /// Lấy thông tin farmer theo ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<ApiResponse<FarmerDto>>> GetFarmer(int id)
    {
        var query = new GetFarmerByIdQuery(id);
        var result = await _mediator.Send(query);
        
        if (result.Success)
            return Ok(result);
        
        if (result.Message == "Không tìm thấy nông dân")
            return NotFound(result);
        
        return BadRequest(result);
    }

    /// <summary>
    /// Tạo farmer mới
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<ApiResponse<FarmerDto>>> CreateFarmer([FromBody] CreateFarmerDto createDto)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values
                .SelectMany(v => v.Errors)
                .Select(e => e.ErrorMessage)
                .ToList();
            
            return BadRequest(ApiResponse<FarmerDto>.FailureResult("Dữ liệu không hợp lệ", errors));
        }

        var command = new CreateFarmerCommand(createDto);
        var result = await _mediator.Send(command);
        
        if (result.Success)
            return CreatedAtAction(nameof(GetFarmer), new { id = result.Data!.Id }, result);
        
        return BadRequest(result);
    }

    /// <summary>
    /// Cập nhật thông tin farmer
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult<ApiResponse<FarmerDto>>> UpdateFarmer(int id, [FromBody] UpdateFarmerDto updateDto)
    {
        if (id != updateDto.Id)
        {
            return BadRequest(ApiResponse<FarmerDto>.FailureResult("ID không khớp"));
        }

        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values
                .SelectMany(v => v.Errors)
                .Select(e => e.ErrorMessage)
                .ToList();
            
            return BadRequest(ApiResponse<FarmerDto>.FailureResult("Dữ liệu không hợp lệ", errors));
        }

        var command = new UpdateFarmerCommand(updateDto);
        var result = await _mediator.Send(command);
        
        if (result.Success)
            return Ok(result);
        
        if (result.Message == "Không tìm thấy nông dân")
            return NotFound(result);
        
        return BadRequest(result);
    }

    /// <summary>
    /// Xóa farmer
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<ActionResult<ApiResponse<bool>>> DeleteFarmer(int id)
    {
        var command = new DeleteFarmerCommand(id);
        var result = await _mediator.Send(command);
        
        if (result.Success)
            return Ok(result);
        
        if (result.Message == "Không tìm thấy nông dân")
            return NotFound(result);
        
        return BadRequest(result);
    }
}
