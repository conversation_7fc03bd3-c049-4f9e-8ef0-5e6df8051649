import React, { useCallback, useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Col,
  Container,
  Form,
  Pagination,
  Row,
  Spinner,
  Table,
} from "react-bootstrap";
import { farmerService } from "../services/farmerService";
import { FarmerDto, PagedResult, SearchFarmerDto } from "../types/farmer";

interface FarmerListProps {
  onEdit: (farmer: FarmerDto) => void;
  onDelete: (farmer: FarmerDto) => void;
  onAdd: () => void;
  refreshTrigger: number;
}

const FarmerList: React.FC<FarmerListProps> = ({
  onEdit,
  onDelete,
  onAdd,
  refreshTrigger,
}) => {
  const [farmers, setFarmers] = useState<PagedResult<FarmerDto>>({
    items: [],
    totalCount: 0,
    pageNumber: 1,
    pageSize: 10,
    totalPages: 0,
    hasPreviousPage: false,
    hasNextPage: false,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>("");
  const [searchParams, setSearchParams] = useState<SearchFarmerDto>({
    farmerCode: "",
    farmerName: "",
    pageNumber: 1,
    pageSize: 10,
  });

  const loadFarmers = useCallback(async () => {
    setLoading(true);
    setError("");
    try {
      const response = await farmerService.getFarmers(searchParams);
      if (response.success && response.data) {
        setFarmers(response.data);
      } else {
        setError(response.message || "Có lỗi xảy ra khi tải danh sách nông dân");
      }
    } catch (err: any) {
      setError(
        err.response?.data?.message || "Có lỗi xảy ra khi tải danh sách nông dân"
      );
    } finally {
      setLoading(false);
    }
  }, [searchParams]);

  useEffect(() => {
    loadFarmers();
  }, [loadFarmers, refreshTrigger]);

  const handleSearch = () => {
    setSearchParams((prev) => ({ ...prev, pageNumber: 1 }));
  };

  const handlePageChange = (page: number) => {
    setSearchParams((prev) => ({ ...prev, pageNumber: page }));
  };

  const handleReset = () => {
    setSearchParams({
      farmerCode: "",
      farmerName: "",
      pageNumber: 1,
      pageSize: 10,
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("vi-VN");
  };

  return (
    <Container fluid>
      <Row className="mb-3">
        <Col>
          <h2>Danh sách Nông dân</h2>
        </Col>
        <Col xs="auto">
          <Button variant="primary" onClick={onAdd}>
            <i className="bi bi-plus-circle me-2"></i>
            Thêm mới
          </Button>
        </Col>
      </Row>

      {/* Search Form */}
      <Row className="mb-3">
        <Col md={4}>
          <Form.Group>
            <Form.Label>Mã nông dân</Form.Label>
            <Form.Control
              type="text"
              placeholder="Nhập mã nông dân..."
              value={searchParams.farmerCode}
              onChange={(e) =>
                setSearchParams((prev) => ({
                  ...prev,
                  farmerCode: e.target.value,
                }))
              }
            />
          </Form.Group>
        </Col>
        <Col md={4}>
          <Form.Group>
            <Form.Label>Tên nông dân</Form.Label>
            <Form.Control
              type="text"
              placeholder="Nhập tên nông dân..."
              value={searchParams.farmerName}
              onChange={(e) =>
                setSearchParams((prev) => ({
                  ...prev,
                  farmerName: e.target.value,
                }))
              }
            />
          </Form.Group>
        </Col>
        <Col md={4} className="d-flex align-items-end">
          <Button
            variant="outline-primary"
            onClick={handleSearch}
            className="me-2"
          >
            <i className="bi bi-search me-1"></i>
            Tìm kiếm
          </Button>
          <Button variant="outline-secondary" onClick={handleReset}>
            <i className="bi bi-arrow-clockwise me-1"></i>
            Làm mới
          </Button>
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" dismissible onClose={() => setError("")}>
          {error}
        </Alert>
      )}

      {loading ? (
        <div className="text-center py-4">
          <Spinner animation="border" role="status">
            <span className="visually-hidden">Đang tải...</span>
          </Spinner>
        </div>
      ) : (
        <>
          <Table striped bordered hover responsive>
            <thead className="table-dark">
              <tr>
                <th>STT</th>
                <th>Mã nông dân</th>
                <th>Tên nông dân</th>
                <th>Tên tiếng Anh</th>
                <th>Địa chỉ</th>
                <th>Điện thoại 1</th>
                <th>Điện thoại 2</th>
                <th>Ngày tạo</th>
                <th>Thao tác</th>
              </tr>
            </thead>
            <tbody>
              {farmers.items.length === 0 ? (
                <tr>
                  <td colSpan={9} className="text-center py-4">
                    Không có dữ liệu
                  </td>
                </tr>
              ) : (
                farmers.items.map((farmer, index) => (
                  <tr key={farmer.id}>
                    <td>
                      {(farmers.pageNumber - 1) * farmers.pageSize + index + 1}
                    </td>
                    <td>{farmer.farmerCode}</td>
                    <td>{farmer.farmerName}</td>
                    <td>{farmer.farmerNameEN || "-"}</td>
                    <td>{farmer.address || "-"}</td>
                    <td>{farmer.phone1 || "-"}</td>
                    <td>{farmer.phone2 || "-"}</td>
                    <td>{formatDate(farmer.insertDate)}</td>
                    <td>
                      <Button
                        variant="outline-primary"
                        size="sm"
                        className="me-2"
                        onClick={() => onEdit(farmer)}
                      >
                        <i className="bi bi-pencil"></i>
                      </Button>
                      <Button
                        variant="outline-danger"
                        size="sm"
                        onClick={() => onDelete(farmer)}
                      >
                        <i className="bi bi-trash"></i>
                      </Button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </Table>

          {/* Pagination */}
          {farmers.totalPages > 1 && (
            <Row>
              <Col className="d-flex justify-content-between align-items-center">
                <div>
                  Hiển thị {farmers.items.length} / {farmers.totalCount} bản ghi
                </div>
                <Pagination>
                  <Pagination.First
                    disabled={!farmers.hasPreviousPage}
                    onClick={() => handlePageChange(1)}
                  />
                  <Pagination.Prev
                    disabled={!farmers.hasPreviousPage}
                    onClick={() => handlePageChange(farmers.pageNumber - 1)}
                  />

                  {[...Array(Math.min(5, farmers.totalPages))].map((_, i) => {
                    const page =
                      Math.max(
                        1,
                        Math.min(
                          farmers.totalPages - 4,
                          farmers.pageNumber - 2
                        )
                      ) + i;
                    return (
                      <Pagination.Item
                        key={page}
                        active={page === farmers.pageNumber}
                        onClick={() => handlePageChange(page)}
                      >
                        {page}
                      </Pagination.Item>
                    );
                  })}

                  <Pagination.Next
                    disabled={!farmers.hasNextPage}
                    onClick={() => handlePageChange(farmers.pageNumber + 1)}
                  />
                  <Pagination.Last
                    disabled={!farmers.hasNextPage}
                    onClick={() => handlePageChange(farmers.totalPages)}
                  />
                </Pagination>
              </Col>
            </Row>
          )}
        </>
      )}
    </Container>
  );
};

export default FarmerList;
