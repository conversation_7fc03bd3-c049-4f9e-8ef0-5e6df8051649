import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import {
  <PERSON><PERSON>,
  <PERSON>ton,
  Card,
  Col,
  Input,
  Row,
  Space,
  Spin,
  Table,
  Typography,
} from "antd";
import type { ColumnsType } from "antd/es/table";
import React, { useCallback, useEffect, useState } from "react";
import { farmerService } from "../services/farmerService";
import { FarmerDto, PagedResult, SearchFarmerDto } from "../types/farmer";

const { Title } = Typography;

interface FarmerListProps {
  onEdit: (farmer: FarmerDto) => void;
  onDelete: (farmer: FarmerDto) => void;
  onAdd: () => void;
  refreshTrigger: number;
}

const FarmerList: React.FC<FarmerListProps> = ({
  onEdit,
  onDelete,
  onAdd,
  refreshTrigger,
}) => {
  const [farmers, setFarmers] = useState<PagedResult<FarmerDto>>({
    items: [],
    totalCount: 0,
    pageNumber: 1,
    pageSize: 10,
    totalPages: 0,
    hasPreviousPage: false,
    hasNextPage: false,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>("");
  const [searchParams, setSearchParams] = useState<SearchFarmerDto>({
    farmerCode: "",
    farmerName: "",
    pageNumber: 1,
    pageSize: 10,
  });

  const loadFarmers = useCallback(async () => {
    setLoading(true);
    setError("");
    try {
      const response = await farmerService.getFarmers(searchParams);
      if (response.success && response.data) {
        setFarmers(response.data);
      } else {
        setError(
          response.message || "Có lỗi xảy ra khi tải danh sách nông dân"
        );
      }
    } catch (err: any) {
      setError(
        err.response?.data?.message ||
          "Có lỗi xảy ra khi tải danh sách nông dân"
      );
    } finally {
      setLoading(false);
    }
  }, [searchParams]);

  useEffect(() => {
    loadFarmers();
  }, [loadFarmers, refreshTrigger]);

  const handleSearch = () => {
    setSearchParams((prev) => ({ ...prev, pageNumber: 1 }));
  };

  const handlePageChange = (page: number) => {
    setSearchParams((prev) => ({ ...prev, pageNumber: page }));
  };

  const handleReset = () => {
    setSearchParams({
      farmerCode: "",
      farmerName: "",
      pageNumber: 1,
      pageSize: 10,
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("vi-VN");
  };

  const columns: ColumnsType<FarmerDto> = [
    {
      title: "STT",
      key: "index",
      width: 60,
      render: (_, __, index) =>
        (farmers.pageNumber - 1) * farmers.pageSize + index + 1,
    },
    {
      title: "Mã nông dân",
      dataIndex: "farmerCode",
      key: "farmerCode",
    },
    {
      title: "Tên nông dân",
      dataIndex: "farmerName",
      key: "farmerName",
    },
    {
      title: "Tên tiếng Anh",
      dataIndex: "farmerNameEN",
      key: "farmerNameEN",
      render: (text) => text || "-",
    },
    {
      title: "Địa chỉ",
      dataIndex: "address",
      key: "address",
      render: (text) => text || "-",
    },
    {
      title: "Điện thoại 1",
      dataIndex: "phone1",
      key: "phone1",
      render: (text) => text || "-",
    },
    {
      title: "Điện thoại 2",
      dataIndex: "phone2",
      key: "phone2",
      render: (text) => text || "-",
    },
    {
      title: "Ngày tạo",
      dataIndex: "insertDate",
      key: "insertDate",
      render: (date) => formatDate(date),
    },
    {
      title: "Thao tác",
      key: "actions",
      width: 120,
      render: (_, farmer) => (
        <Space>
          <Button
            type="primary"
            ghost
            size="small"
            icon={<EditOutlined />}
            onClick={() => onEdit(farmer)}
          />
          <Button
            type="primary"
            danger
            ghost
            size="small"
            icon={<DeleteOutlined />}
            onClick={() => onDelete(farmer)}
          />
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <Title level={2}>Danh sách Nông dân</Title>
        </Col>
        <Col>
          <Button type="primary" icon={<PlusOutlined />} onClick={onAdd}>
            Thêm mới
          </Button>
        </Col>
      </Row>

      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <Input
              placeholder="Nhập mã nông dân..."
              value={searchParams.farmerCode}
              onChange={(e) =>
                setSearchParams((prev) => ({
                  ...prev,
                  farmerCode: e.target.value,
                }))
              }
              addonBefore="Mã nông dân"
            />
          </Col>
          <Col span={8}>
            <Input
              placeholder="Nhập tên nông dân..."
              value={searchParams.farmerName}
              onChange={(e) =>
                setSearchParams((prev) => ({
                  ...prev,
                  farmerName: e.target.value,
                }))
              }
              addonBefore="Tên nông dân"
            />
          </Col>
          <Col span={8}>
            <Space>
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={handleSearch}
              >
                Tìm kiếm
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleReset}>
                Làm mới
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {error && (
        <Alert
          message={error}
          type="error"
          closable
          onClose={() => setError("")}
          style={{ marginBottom: 16 }}
        />
      )}

      <Spin spinning={loading}>
        <Table
          columns={columns}
          dataSource={farmers.items}
          rowKey="id"
          pagination={{
            current: farmers.pageNumber,
            pageSize: farmers.pageSize,
            total: farmers.totalCount,
            showSizeChanger: false,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} của ${total} bản ghi`,
            onChange: handlePageChange,
          }}
          locale={{
            emptyText: "Không có dữ liệu",
          }}
        />
      </Spin>
    </div>
  );
};

export default FarmerList;
