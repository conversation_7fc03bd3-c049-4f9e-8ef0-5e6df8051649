using FarmerManager.Application.Common.Models;
using FarmerManager.Application.DTOs.Farmer;
using FarmerManager.Domain.Entities;

namespace FarmerManager.Application.Contracts.Repositories;

public interface IFarmerRepository
{
    Task<PagedResult<Farmer>> GetAllAsync(SearchFarmerDto searchDto, CancellationToken cancellationToken = default);
    Task<Farmer?> GetByIdAsync(int id, CancellationToken cancellationToken = default);
    Task<Farmer?> GetByCodeAsync(string farmerCode, CancellationToken cancellationToken = default);
    Task<bool> ExistsByCodeAsync(string farmerCode, CancellationToken cancellationToken = default);
    Task<bool> ExistsByCodeAsync(string farmerCode, int excludeId, CancellationToken cancellationToken = default);
    Task<Farmer> CreateAsync(Farmer farmer, CancellationToken cancellationToken = default);
    Task<Farmer> UpdateAsync(Farmer farmer, CancellationToken cancellationToken = default);
    Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default);
}
