using FarmerManager.Domain.Entities;
using Microsoft.EntityFrameworkCore;

namespace FarmerManager.Persistence.Data;

public class FarmerManagerDbContext : DbContext
{
    public FarmerManagerDbContext(DbContextOptions<FarmerManagerDbContext> options) : base(options)
    {
    }

    public DbSet<Farmer> Farmers { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Apply configurations
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(FarmerManagerDbContext).Assembly);
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Auto-update tracking fields
        var entries = ChangeTracker.Entries()
            .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified);

        foreach (var entry in entries)
        {
            if (entry.Entity is Farmer farmer)
            {
                if (entry.State == EntityState.Added)
                {
                    farmer.InsertDate = DateTime.Now;
                }
                else if (entry.State == EntityState.Modified)
                {
                    farmer.UpdatedDate = DateTime.Now;
                }
            }
        }

        return await base.SaveChangesAsync(cancellationToken);
    }
}
