import "bootstrap-icons/font/bootstrap-icons.css";
import "bootstrap/dist/css/bootstrap.min.css";
import React, { useState } from "react";
import { Container, Navbar, Toast, ToastContainer } from "react-bootstrap";
import DeleteConfirmModal from "./components/DeleteConfirmModal";
import FarmerForm from "./components/FarmerForm";
import FarmerList from "./components/FarmerList";
import { FarmerDto } from "./types/farmer";

const App: React.FC = () => {
  const [showForm, setShowForm] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedFarmer, setSelectedFarmer] = useState<FarmerDto | undefined>();
  const [formMode, setFormMode] = useState<"create" | "edit">("create");
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [toast, setToast] = useState<{
    show: boolean;
    message: string;
    variant: string;
  }>({
    show: false,
    message: "",
    variant: "success",
  });

  const handleAdd = () => {
    setSelectedFarmer(undefined);
    setFormMode("create");
    setShowForm(true);
  };

  const handleEdit = (farmer: FarmerDto) => {
    setSelectedFarmer(farmer);
    setFormMode("edit");
    setShowForm(true);
  };

  const handleDelete = (farmer: FarmerDto) => {
    setSelectedFarmer(farmer);
    setShowDeleteModal(true);
  };

  const handleFormSuccess = () => {
    setRefreshTrigger((prev) => prev + 1);
    showToast(
      formMode === "create"
        ? "Thêm mới nông dân thành công!"
        : "Cập nhật nông dân thành công!",
      "success"
    );
  };

  const handleDeleteSuccess = () => {
    setRefreshTrigger((prev) => prev + 1);
    showToast("Xóa nông dân thành công!", "success");
  };

  const showToast = (message: string, variant: string = "success") => {
    setToast({ show: true, message, variant });
  };

  return (
    <div className="App">
      <Navbar bg="dark" variant="dark" className="mb-4">
        <Container>
          <Navbar.Brand>
            <i className="bi bi-people-fill me-2"> </i>
            Hệ thống quản lý Nông dân
          </Navbar.Brand>
        </Container>
      </Navbar>

      <Container fluid>
        <FarmerList
          onAdd={handleAdd}
          onEdit={handleEdit}
          onDelete={handleDelete}
          refreshTrigger={refreshTrigger}
        />
      </Container>

      <FarmerForm
        show={showForm}
        onHide={() => setShowForm(false)}
        onSuccess={handleFormSuccess}
        farmer={selectedFarmer}
        mode={formMode}
      />

      <DeleteConfirmModal
        show={showDeleteModal}
        onHide={() => setShowDeleteModal(false)}
        onSuccess={handleDeleteSuccess}
        farmer={selectedFarmer}
      />

      <ToastContainer position="top-end" className="p-3">
        <Toast
          show={toast.show}
          onClose={() => setToast((prev) => ({ ...prev, show: false }))}
          delay={3000}
          autohide
          bg={toast.variant}
        >
          <Toast.Header>
            <strong className="me-auto">
              {toast.variant === "success" ? "Thành công" : "Thông báo"}
            </strong>
          </Toast.Header>
          <Toast.Body
            className={toast.variant === "success" ? "text-white" : ""}
          >
            {toast.message}
          </Toast.Body>
        </Toast>
      </ToastContainer>
    </div>
  );
};

export default App;
