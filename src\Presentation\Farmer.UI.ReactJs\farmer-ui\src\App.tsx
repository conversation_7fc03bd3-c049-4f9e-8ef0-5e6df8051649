import React, { useState } from "react";
import { Layout, Typography, notification } from "antd";
import { UserOutlined } from "@ant-design/icons";
import "antd/dist/reset.css";
import DeleteConfirmModal from "./components/DeleteConfirmModal";
import FarmerForm from "./components/FarmerForm";
import FarmerList from "./components/FarmerList";
import type { FarmerDto } from "./types/farmer";

const { Header, Content } = Layout;
const { Title } = Typography;

function App() {
  const [showForm, setShowForm] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedFarmer, setSelectedFarmer] = useState<FarmerDto | undefined>();
  const [formMode, setFormMode] = useState<"create" | "edit">("create");
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleAdd = () => {
    setSelectedFarmer(undefined);
    setFormMode("create");
    setShowForm(true);
  };

  const handleEdit = (farmer: FarmerDto) => {
    setSelectedFarmer(farmer);
    setFormMode("edit");
    setShowForm(true);
  };

  const handleDelete = (farmer: FarmerDto) => {
    setSelectedFarmer(farmer);
    setShowDeleteModal(true);
  };

  const handleFormSuccess = () => {
    setRefreshTrigger((prev) => prev + 1);
    notification.success({
      message: "Thành công",
      description:
        formMode === "create"
          ? "Thêm mới nông dân thành công!"
          : "Cập nhật nông dân thành công!",
      placement: "topRight",
    });
  };

  const handleDeleteSuccess = () => {
    setRefreshTrigger((prev) => prev + 1);
    notification.success({
      message: "Thành công",
      description: "Xóa nông dân thành công!",
      placement: "topRight",
    });
  };

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <Header
        style={{
          backgroundColor: "#001529",
          padding: "0 24px",
          display: "flex",
          alignItems: "center",
        }}
      >
        <UserOutlined
          style={{ fontSize: "20px", color: "white", marginRight: "12px" }}
        />
        <Title level={3} style={{ color: "white", margin: 0 }}>
          Hệ thống quản lý Nông dân
        </Title>
      </Header>

      <Content style={{ padding: "24px" }}>
        <FarmerList
          onAdd={handleAdd}
          onEdit={handleEdit}
          onDelete={handleDelete}
          refreshTrigger={refreshTrigger}
        />
      </Content>

      <FarmerForm
        show={showForm}
        onHide={() => setShowForm(false)}
        onSuccess={handleFormSuccess}
        farmer={selectedFarmer}
        mode={formMode}
      />

      <DeleteConfirmModal
        show={showDeleteModal}
        onHide={() => setShowDeleteModal(false)}
        onSuccess={handleDeleteSuccess}
        farmer={selectedFarmer}
      />
    </Layout>
  );
}

export default App;
