using System.Text.RegularExpressions;

namespace FarmerManager.Shared.Extensions;

public static class StringExtensions
{
    public static bool IsValidFarmerCode(this string farmerCode)
    {
        if (string.IsNullOrWhiteSpace(farmerCode))
            return false;

        return Regex.IsMatch(farmerCode, @"^[A-Za-z0-9_-]+$");
    }

    public static bool IsValidPhoneNumber(this string phoneNumber)
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
            return true; // Phone is optional

        return Regex.IsMatch(phoneNumber, @"^[\d\s\-\+\(\)]+$");
    }

    public static string ToTitleCase(this string input)
    {
        if (string.IsNullOrWhiteSpace(input))
            return string.Empty;

        return System.Globalization.CultureInfo.CurrentCulture.TextInfo.ToTitleCase(input.ToLower());
    }

    public static string RemoveExtraSpaces(this string input)
    {
        if (string.IsNullOrWhiteSpace(input))
            return string.Empty;

        return Regex.Replace(input.Trim(), @"\s+", " ");
    }
}
