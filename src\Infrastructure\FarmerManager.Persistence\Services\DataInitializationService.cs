using FarmerManager.Domain.Entities;
using FarmerManager.Persistence.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace FarmerManager.Persistence.Services;

public class DataInitializationService : IDataInitializationService
{
    private readonly FarmerManagerDbContext _context;
    private readonly ILogger<DataInitializationService> _logger;

    public DataInitializationService(FarmerManagerDbContext context, ILogger<DataInitializationService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task InitializeAsync()
    {
        try
        {
            // Ensure database is created
            await _context.Database.EnsureCreatedAsync();
            _logger.LogInformation("Database ensured created");

            // Check if data already exists
            if (await _context.Farmers.AnyAsync())
            {
                _logger.LogInformation("Database already contains data. Skipping initialization.");
                return;
            }

            _logger.LogInformation("Initializing database with sample data...");

            // Sample data from JSON
            var sampleFarmers = new List<Farmer>
            {
                new() { FarmerCode = "NS001", FarmerName = "<PERSON><PERSON><PERSON><PERSON>", Phone1 = "0912345678", Address = "25/7 <PERSON><PERSON>, Quận 10, TP. <PERSON><PERSON> Chí <PERSON>" },
                new() { FarmerCode = "NS002", FarmerName = "Trần Thị Mai", Phone1 = "0903876543", Address = "102 Nguyễn Huệ, Quận 1, TP. Hồ Chí Minh" },
                new() { FarmerCode = "NS003", FarmerName = "Lê Hoàng Phúc", Phone1 = "0981234567", Address = "45 Lê Duẩn, Quận Hải Châu, Đà Nẵng" },
                new() { FarmerCode = "NS004", FarmerName = "Phạm Minh Tuấn", Phone1 = "0934567890", Address = "12 Hoàng Văn Thụ, TP. Thủ Dầu Một, Bình Dương" },
                new() { FarmerCode = "NS005", FarmerName = "Đỗ Thị Lan Anh", Phone1 = "0972345123", Address = "86 Nguyễn Trãi, Thanh Xuân, Hà Nội" },
                new() { FarmerCode = "NS006", FarmerName = "Võ Quốc Bảo", Phone1 = "0968123456", Address = "19 Trần Hưng Đạo, Nha Trang, Khánh Hòa" },
                new() { FarmerCode = "NS007", FarmerName = "Ngô Thanh Tùng", Phone1 = "0923456789", Address = "77 Hùng Vương, TP. Huế" },
                new() { FarmerCode = "NS008", FarmerName = "Bùi Thị Ngọc Hân", Phone1 = "0945678901", Address = "34 Phạm Văn Đồng, TP. Quy Nhơn, Bình Định" },
                new() { FarmerCode = "NS009", FarmerName = "Huỳnh Đức Thịnh", Phone1 = "0917654321", Address = "210 Điện Biên Phủ, Quận Bình Thạnh, TP. Hồ Chí Minh" },
                new() { FarmerCode = "NS010", FarmerName = "Nguyễn Thị Thu Hà", Phone1 = "0938765432", Address = "58 Lạc Long Quân, Quận Tân Bình, TP. Hồ Chí Minh" },
                new() { FarmerCode = "NS011", FarmerName = "Trần Quang Vinh", Phone1 = "0905123456", Address = "12 Nguyễn Văn Linh, Quận Hải Châu, Đà Nẵng" },
                new() { FarmerCode = "NS012", FarmerName = "Phạm Thị Hồng", Phone1 = "0939456123", Address = "90 Lê Lợi, TP. Vinh, Nghệ An" },
                new() { FarmerCode = "NS013", FarmerName = "Nguyễn Hoàng Nam", Phone1 = "0978234561", Address = "33 Hai Bà Trưng, Quận Hoàn Kiếm, Hà Nội" },
                new() { FarmerCode = "NS014", FarmerName = "Đặng Văn Toàn", Phone1 = "0912789456", Address = "12 Bạch Đằng, TP. Đà Nẵng" },
                new() { FarmerCode = "NS015", FarmerName = "Vũ Thị Minh Ngọc", Phone1 = "0945789321", Address = "85 Phan Chu Trinh, TP. Hội An, Quảng Nam" },
                new() { FarmerCode = "NS016", FarmerName = "Nguyễn Thành Đạt", Phone1 = "0987651234", Address = "140 Lê Hồng Phong, TP. Nha Trang, Khánh Hòa" },
                new() { FarmerCode = "NS017", FarmerName = "Phan Thị Bích", Phone1 = "0928345671", Address = "66 Nguyễn Khuyến, Quận Đống Đa, Hà Nội" },
                new() { FarmerCode = "NS018", FarmerName = "Lương Văn Khánh", Phone1 = "0978123567", Address = "21 Nguyễn Văn Cừ, TP. Bắc Giang" },
                new() { FarmerCode = "NS019", FarmerName = "Nguyễn Hữu Tài", Phone1 = "0961782345", Address = "55 Trần Phú, TP. Đà Lạt, Lâm Đồng" },
                new() { FarmerCode = "NS020", FarmerName = "Đỗ Mạnh Hùng", Phone1 = "0909876543", Address = "18 Nguyễn Văn Trỗi, TP. Vũng Tàu" },
                new() { FarmerCode = "NS021", FarmerName = "Nguyễn Thị Kim Oanh", Phone1 = "0934567123", Address = "45 Hoàng Hoa Thám, Quận Ba Đình, Hà Nội" },
                new() { FarmerCode = "NS022", FarmerName = "Trần Quốc Việt", Phone1 = "0971654321", Address = "72 Nguyễn Tất Thành, TP. Pleiku, Gia Lai" },
                new() { FarmerCode = "NS023", FarmerName = "Lê Thị Thu Trang", Phone1 = "0912983456", Address = "88 Cách Mạng Tháng Tám, Quận 3, TP. Hồ Chí Minh" },
                new() { FarmerCode = "NS024", FarmerName = "Nguyễn Văn Dũng", Phone1 = "0956781234", Address = "10 Nguyễn Văn Cừ, TP. Cần Thơ" },
                new() { FarmerCode = "NS025", FarmerName = "Phạm Thị Yến", Phone1 = "0923456123", Address = "22 Bùi Thị Xuân, TP. Huế" },
                new() { FarmerCode = "NS026", FarmerName = "Nguyễn Minh Hoàng", Phone1 = "0967123456", Address = "37 Nguyễn Thị Minh Khai, TP. Nha Trang, Khánh Hòa" },
                new() { FarmerCode = "NS027", FarmerName = "Trần Thị Hoa", Phone1 = "0987234561", Address = "16 Trần Quang Khải, TP. Hà Nội" },
                new() { FarmerCode = "NS028", FarmerName = "Nguyễn Tuấn Kiệt", Phone1 = "0917123987", Address = "27 Nguyễn Hữu Thọ, TP. Đà Nẵng" },
                new() { FarmerCode = "NS029", FarmerName = "Hoàng Thị Hạnh", Phone1 = "0938712345", Address = "60 Trần Hưng Đạo, TP. Quy Nhơn" },
                new() { FarmerCode = "NS030", FarmerName = "Nguyễn Văn Long", Phone1 = "0902783456", Address = "12 Trường Chinh, TP. Buôn Ma Thuột, Đắk Lắk" }
            };

            await _context.Farmers.AddRangeAsync(sampleFarmers);
            await _context.SaveChangesAsync();

            _logger.LogInformation($"Successfully initialized database with {sampleFarmers.Count} farmers");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while initializing the database");
            throw;
        }
    }
}
