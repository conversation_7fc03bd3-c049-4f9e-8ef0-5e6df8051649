using FluentValidation;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;
using FarmerManager.Application.Services;
using FarmerManager.Application.Services.Interfaces;

namespace FarmerManager.Application;

public static class DependencyInjection
{
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        var assembly = Assembly.GetExecutingAssembly();

        // Add MediatR
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(assembly));

        // Register all handlers in the assembly
        services.AddTransient<IFarmerService, FarmerService>();
        
        // Add FluentValidation
        services.AddValidatorsFromAssembly(assembly);

        return services;
    }
}
