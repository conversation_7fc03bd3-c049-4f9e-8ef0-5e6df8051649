export interface FarmerDto {
  id: number;
  farmerCode: string;
  farmerName: string;
  farmerNameEN?: string;
  address?: string;
  phone1?: string;
  phone2?: string;
  insertDate: string;
  updatedDate?: string;
}

export interface CreateFarmerDto {
  farmerCode: string;
  farmerName: string;
  farmerNameEN?: string;
  address?: string;
  phone1?: string;
  phone2?: string;
}

export interface UpdateFarmerDto {
  id: number;
  farmerCode: string;
  farmerName: string;
  farmerNameEN?: string;
  address?: string;
  phone1?: string;
  phone2?: string;
}

export interface SearchFarmerDto {
  farmerCode?: string;
  farmerName?: string;
  pageNumber: number;
  pageSize: number;
}

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data?: T;
  errors: string[];
}

export interface PagedResult<T> {
  items: T[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
}
