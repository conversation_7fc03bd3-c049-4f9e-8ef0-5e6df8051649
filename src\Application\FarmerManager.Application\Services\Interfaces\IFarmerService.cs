using FarmerManager.Application.Common.Models;
using FarmerManager.Application.DTOs.Farmer;

namespace FarmerManager.Application.Services.Interfaces;

public interface IFarmerService
{
    Task<ApiResponse<PagedResult<FarmerDto>>> GetAllFarmersAsync(SearchFarmerDto searchDto, CancellationToken cancellationToken = default);
    Task<ApiResponse<FarmerDto>> GetFarmerByIdAsync(int id, CancellationToken cancellationToken = default);
    Task<ApiResponse<FarmerDto>> CreateFarmerAsync(CreateFarmerDto createDto, CancellationToken cancellationToken = default);
    Task<ApiResponse<FarmerDto>> UpdateFarmerAsync(UpdateFarmerDto updateDto, CancellationToken cancellationToken = default);
    Task<ApiResponse<bool>> DeleteFarmerAsync(int id, CancellationToken cancellationToken = default);
}
