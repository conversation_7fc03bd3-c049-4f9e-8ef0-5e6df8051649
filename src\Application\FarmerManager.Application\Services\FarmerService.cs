using FarmerManager.Application.Common.Models;
using FarmerManager.Application.Contracts.Repositories;
using FarmerManager.Application.DTOs.Farmer;
using FarmerManager.Application.Services.Interfaces;
using FarmerManager.Domain.Entities;

namespace FarmerManager.Application.Services;

public class FarmerService : IFarmerService
{
    private readonly IFarmerRepository _farmerRepository;

    public FarmerService(IFarmerRepository farmerRepository)
    {
        _farmerRepository = farmerRepository;
    }

    public async Task<ApiResponse<PagedResult<FarmerDto>>> GetAllFarmersAsync(SearchFarmerDto searchDto, CancellationToken cancellationToken = default)
    {
        var farmers = await _farmerRepository.GetAllAsync(searchDto, cancellationToken);

        var farmerDtos = farmers.Items.Select(MapToDto).ToList();
        var result = new PagedResult<FarmerDto>(farmerDtos, farmers.TotalCount, farmers.PageNumber, farmers.PageSize);

        return ApiResponse<PagedResult<FarmerDto>>.SuccessResult(result, "L<PERSON>y danh sách nông dân thành công");
    }

    public async Task<ApiResponse<FarmerDto>> GetFarmerByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        var farmer = await _farmerRepository.GetByIdAsync(id, cancellationToken);
        if (farmer == null)
        {
            return ApiResponse<FarmerDto>.FailureResult("Không tìm thấy nông dân");
        }

        return ApiResponse<FarmerDto>.SuccessResult(MapToDto(farmer), "Lấy thông tin nông dân thành công");
    }

    public async Task<ApiResponse<FarmerDto>> CreateFarmerAsync(CreateFarmerDto createDto, CancellationToken cancellationToken = default)
    {
        var existingFarmer = await _farmerRepository.ExistsByCodeAsync(createDto.FarmerCode, cancellationToken);
        if (existingFarmer)
        {
            return ApiResponse<FarmerDto>.FailureResult("Mã nông dân đã tồn tại");
        }

        var farmer = new Farmer
        {
            FarmerCode = createDto.FarmerCode,
            FarmerName = createDto.FarmerName,
            FarmerNameEN = createDto.FarmerNameEN,
            Address = createDto.Address,
            Phone1 = createDto.Phone1,
            Phone2 = createDto.Phone2,
            InsertDate = DateTime.Now
        };

        var createdFarmer = await _farmerRepository.CreateAsync(farmer, cancellationToken);

        var farmerDto = MapToDto(createdFarmer);

        return ApiResponse<FarmerDto>.SuccessResult(farmerDto, "Tạo nông dân thành công");
    }

    public async Task<ApiResponse<FarmerDto>> UpdateFarmerAsync(UpdateFarmerDto updateDto, CancellationToken cancellationToken = default)
    {
        var existingFarmer = await _farmerRepository.GetByIdAsync(updateDto.Id, cancellationToken);
        if (existingFarmer == null)
        {
            return ApiResponse<FarmerDto>.FailureResult("Không tìm thấy nông dân");
        }

        // Kiểm tra FarmerCode đã tồn tại chưa (trừ chính nó)
        var codeExists = await _farmerRepository.ExistsByCodeAsync(updateDto.FarmerCode, updateDto.Id, cancellationToken);
        if (codeExists)
        {
            return ApiResponse<FarmerDto>.FailureResult("Mã nông dân đã tồn tại");
        }

        existingFarmer.FarmerName = updateDto.FarmerName;
        existingFarmer.FarmerNameEN = updateDto.FarmerNameEN;
        existingFarmer.Address = updateDto.Address;
        existingFarmer.Phone1 = updateDto.Phone1;
        existingFarmer.Phone2 = updateDto.Phone2;
        existingFarmer.UpdatedDate = DateTime.Now;

        var updatedFarmer = await _farmerRepository.UpdateAsync(existingFarmer, cancellationToken);
        return ApiResponse<FarmerDto>.SuccessResult(MapToDto(updatedFarmer), "Cập nhật nông dân thành công");
    }

    public async Task<ApiResponse<bool>> DeleteFarmerAsync(int id, CancellationToken cancellationToken = default)
    {
        var existingFarmer = await _farmerRepository.GetByIdAsync(id, cancellationToken);
        if (existingFarmer == null)
        {
            return ApiResponse<bool>.FailureResult("Không tìm thấy nông dân");
        }

        var result = await _farmerRepository.DeleteAsync(id, cancellationToken);
        return result 
            ? ApiResponse<bool>.SuccessResult(true, "Xóa nông dân thành công") 
            : ApiResponse<bool>.FailureResult("Không thể xóa nông dân");
    }

    private static FarmerDto MapToDto(Farmer farmer)
    {
        return new FarmerDto
        {
            Id = farmer.Id,
            FarmerCode = farmer.FarmerCode,
            FarmerName = farmer.FarmerName,
            FarmerNameEN = farmer.FarmerNameEN,
            Address = farmer.Address,
            Phone1 = farmer.Phone1,
            Phone2 = farmer.Phone2,
            InsertDate = farmer.InsertDate,
            UpdatedDate = farmer.UpdatedDate
        };
    }
}
