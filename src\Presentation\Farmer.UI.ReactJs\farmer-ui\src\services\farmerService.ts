import api from './api';
import { 
  FarmerDto, 
  CreateFarmerDto, 
  UpdateFarmerDto, 
  SearchFarmerDto, 
  ApiResponse, 
  PagedResult 
} from '../types/farmer';

export const farmerService = {
  // Get all farmers with search and pagination
  async getFarmers(searchParams: SearchFarmerDto): Promise<ApiResponse<PagedResult<FarmerDto>>> {
    const response = await api.get('/farmers', { params: searchParams });
    return response.data;
  },

  // Get farmer by ID
  async getFarmerById(id: number): Promise<ApiResponse<FarmerDto>> {
    const response = await api.get(`/farmers/${id}`);
    return response.data;
  },

  // Create new farmer
  async createFarmer(farmer: CreateFarmerDto): Promise<ApiResponse<FarmerDto>> {
    const response = await api.post('/farmers', farmer);
    return response.data;
  },

  // Update farmer
  async updateFarmer(id: number, farmer: UpdateFarmerDto): Promise<ApiResponse<FarmerDto>> {
    const response = await api.put(`/farmers/${id}`, farmer);
    return response.data;
  },

  // Delete farmer
  async deleteFarmer(id: number): Promise<ApiResponse<boolean>> {
    const response = await api.delete(`/farmers/${id}`);
    return response.data;
  }
};
