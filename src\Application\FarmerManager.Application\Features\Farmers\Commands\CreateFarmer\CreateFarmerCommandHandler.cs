using FarmerManager.Application.Common.Models;
using FarmerManager.Application.Contracts.Repositories;
using FarmerManager.Application.DTOs.Farmer;
using FarmerManager.Application.Services.Interfaces;
using MediatR;

namespace FarmerManager.Application.Features.Farmers.Commands.CreateFarmer;

public class CreateFarmerCommandHandler : IRequestHandler<CreateFarmerCommand, ApiResponse<FarmerDto>>
{
    private readonly IFarmerService _farmerService;
    public CreateFarmerCommandHandler(IFarmerRepository farmerRepository, IFarmerService farmerService)
    {
        _farmerService = farmerService;
    }

    public async Task<ApiResponse<FarmerDto>> Handle(CreateFarmerCommand request, CancellationToken cancellationToken)
    {
        return await _farmerService.CreateFarmerAsync(request.CreateFarmerDto, cancellationToken);
    }
}
