import { DeleteOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Button, Descriptions, Modal, Typography } from "antd";
import React, { useState } from "react";
import { farmerService } from "../services/farmerService";
import { FarmerDto } from "../types/farmer";

const { Text } = Typography;

interface DeleteConfirmModalProps {
  show: boolean;
  onHide: () => void;
  onSuccess: () => void;
  farmer?: FarmerDto;
}

const DeleteConfirmModal: React.FC<DeleteConfirmModalProps> = ({
  show,
  onHide,
  onSuccess,
  farmer,
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>("");

  const handleDelete = async () => {
    if (!farmer) return;

    setLoading(true);
    setError("");

    try {
      const response = await farmerService.deleteFarmer(farmer.id);
      if (response.success) {
        onSuccess();
        onHide();
      } else {
        setError(response.message);
      }
    } catch (err: any) {
      setError(err.response?.data?.message || "Có lỗi xảy ra khi xóa nông dân");
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setError("");
      onHide();
    }
  };

  return (
    <Modal
      title={
        <span style={{ color: "#ff4d4f" }}>
          <ExclamationCircleOutlined style={{ marginRight: 8 }} />
          Xác nhận xóa
        </span>
      }
      open={show}
      onCancel={handleClose}
      footer={[
        <Button key="cancel" onClick={handleClose} disabled={loading}>
          Hủy
        </Button>,
        <Button
          key="delete"
          type="primary"
          danger
          loading={loading}
          icon={<DeleteOutlined />}
          onClick={handleDelete}
        >
          Xóa
        </Button>,
      ]}
      centered
      width={500}
    >
      {error && (
        <Alert
          message={error}
          type="error"
          closable
          style={{ marginBottom: 16 }}
        />
      )}

      {farmer && (
        <div>
          <p>Bạn có chắc chắn muốn xóa nông dân sau không?</p>

          <Descriptions
            bordered
            size="small"
            column={1}
            style={{ marginBottom: 16 }}
          >
            <Descriptions.Item label="Mã nông dân">
              {farmer.farmerCode}
            </Descriptions.Item>
            <Descriptions.Item label="Tên nông dân">
              {farmer.farmerName}
            </Descriptions.Item>
            {farmer.farmerNameEN && (
              <Descriptions.Item label="Tên tiếng Anh">
                {farmer.farmerNameEN}
              </Descriptions.Item>
            )}
            {farmer.address && (
              <Descriptions.Item label="Địa chỉ">
                {farmer.address}
              </Descriptions.Item>
            )}
          </Descriptions>

          <Text type="danger">
            <ExclamationCircleOutlined style={{ marginRight: 4 }} />
            <strong>Lưu ý:</strong> Hành động này không thể hoàn tác!
          </Text>
        </div>
      )}
    </Modal>
  );
};

export default DeleteConfirmModal;
