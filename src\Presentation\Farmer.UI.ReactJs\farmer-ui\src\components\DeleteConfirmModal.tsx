import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Spinner } from "react-bootstrap";
import { farmerService } from "../services/farmerService";
import { FarmerDto } from "../types/farmer";

interface DeleteConfirmModalProps {
  show: boolean;
  onHide: () => void;
  onSuccess: () => void;
  farmer?: FarmerDto;
}

const DeleteConfirmModal: React.FC<DeleteConfirmModalProps> = ({
  show,
  onHide,
  onSuccess,
  farmer,
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>("");

  const handleDelete = async () => {
    if (!farmer) return;

    setLoading(true);
    setError("");

    try {
      const response = await farmerService.deleteFarmer(farmer.id);
      if (response.success) {
        onSuccess();
        onHide();
      } else {
        setError(response.message);
      }
    } catch (err: any) {
      setError(err.response?.data?.message || "Có lỗi xảy ra khi xóa nông dân");
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setError("");
      onHide();
    }
  };

  return (
    <Modal show={show} onHide={handleClose} centered>
      <Modal.Header closeButton>
        <Modal.Title className="text-danger">
          <i className="bi bi-exclamation-triangle me-2"></i>
          Xác nhận xóa
        </Modal.Title>
      </Modal.Header>

      <Modal.Body>
        {error && <Alert variant="danger">{error}</Alert>}

        {farmer && (
          <div>
            <p>Bạn có chắc chắn muốn xóa nông dân sau không?</p>
            <div className="bg-light p-3 rounded">
              <div className="row">
                <div className="col-sm-4">
                  <strong>Mã nông dân:</strong>
                </div>
                <div className="col-sm-8">{farmer.farmerCode}</div>
              </div>
              <div className="row">
                <div className="col-sm-4">
                  <strong>Tên nông dân:</strong>
                </div>
                <div className="col-sm-8">{farmer.farmerName}</div>
              </div>
              {farmer.farmerNameEN && (
                <div className="row">
                  <div className="col-sm-4">
                    <strong>Tên tiếng Anh:</strong>
                  </div>
                  <div className="col-sm-8">{farmer.farmerNameEN}</div>
                </div>
              )}
              {farmer.address && (
                <div className="row">
                  <div className="col-sm-4">
                    <strong>Địa chỉ:</strong>
                  </div>
                  <div className="col-sm-8">{farmer.address}</div>
                </div>
              )}
            </div>
            <p className="text-danger mt-3 mb-0">
              <i className="bi bi-exclamation-triangle me-1"></i>
              <strong>Lưu ý:</strong> Hành động này không thể hoàn tác!
            </p>
          </div>
        )}
      </Modal.Body>

      <Modal.Footer>
        <Button variant="secondary" onClick={handleClose} disabled={loading}>
          Hủy
        </Button>
        <Button variant="danger" onClick={handleDelete} disabled={loading}>
          {loading && <Spinner animation="border" size="sm" className="me-2" />}
          <i className="bi bi-trash me-1"></i>
          Xóa
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default DeleteConfirmModal;
