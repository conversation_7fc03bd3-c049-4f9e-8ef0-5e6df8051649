﻿using System.ComponentModel.DataAnnotations;
using FarmerManager.Domain.Entities.Base;

namespace FarmerManager.Domain.Entities;

public class Farmer : Entity<int>
{
  [Required]
  [StringLength(50)]
  public string FarmerCode { get; set; } = string.Empty;

  [Required]
  [StringLength(200)]
  public string FarmerName { get; set; } = string.Empty;

  [StringLength(200)]
  public string? FarmerNameEN { get; set; }

  [StringLength(500)]
  public string? Address { get; set; }

  [StringLength(20)]
  public string? Phone1 { get; set; }

  [StringLength(20)]
  public string? Phone2 { get; set; }

  public DateTime InsertDate { get; set; }

  public DateTime? UpdatedDate { get; set; }
}