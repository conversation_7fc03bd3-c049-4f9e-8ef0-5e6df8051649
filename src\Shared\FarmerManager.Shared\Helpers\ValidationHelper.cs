using FarmerManager.Constants;
using System.ComponentModel.DataAnnotations;

namespace FarmerManager.Shared.Helpers;

public static class ValidationHelper
{
    public static List<ValidationResult> ValidateObject<T>(T obj) where T : class
    {
        var validationResults = new List<ValidationResult>();
        var validationContext = new ValidationContext(obj);
        
        Validator.TryValidateObject(obj, validationContext, validationResults, true);
        
        return validationResults;
    }

    public static bool IsValidFarmerCode(string farmerCode, out string errorMessage)
    {
        errorMessage = string.Empty;

        if (string.IsNullOrWhiteSpace(farmerCode))
        {
            errorMessage = FarmerConstants.ValidationMessages.FarmerCodeRequired;
            return false;
        }

        if (farmerCode.Length > FarmerConstants.FieldLengths.FarmerCodeMaxLength)
        {
            errorMessage = string.Format(FarmerConstants.ValidationMessages.FarmerCodeMaxLength, 
                FarmerConstants.FieldLengths.FarmerCodeMaxLength);
            return false;
        }

        if (!System.Text.RegularExpressions.Regex.IsMatch(farmerCode, FarmerConstants.RegexPatterns.FarmerCodePattern))
        {
            errorMessage = FarmerConstants.ValidationMessages.FarmerCodeInvalidFormat;
            return false;
        }

        return true;
    }

    public static bool IsValidFarmerName(string farmerName, out string errorMessage)
    {
        errorMessage = string.Empty;

        if (string.IsNullOrWhiteSpace(farmerName))
        {
            errorMessage = FarmerConstants.ValidationMessages.FarmerNameRequired;
            return false;
        }

        if (farmerName.Length > FarmerConstants.FieldLengths.FarmerNameMaxLength)
        {
            errorMessage = string.Format(FarmerConstants.ValidationMessages.FarmerNameMaxLength, 
                FarmerConstants.FieldLengths.FarmerNameMaxLength);
            return false;
        }

        return true;
    }

    public static bool IsValidPhoneNumber(string phoneNumber, out string errorMessage)
    {
        errorMessage = string.Empty;

        if (string.IsNullOrWhiteSpace(phoneNumber))
            return true; // Phone is optional

        if (phoneNumber.Length > FarmerConstants.FieldLengths.PhoneMaxLength)
        {
            errorMessage = string.Format(FarmerConstants.ValidationMessages.PhoneMaxLength, 
                FarmerConstants.FieldLengths.PhoneMaxLength);
            return false;
        }

        if (!System.Text.RegularExpressions.Regex.IsMatch(phoneNumber, FarmerConstants.RegexPatterns.PhonePattern))
        {
            errorMessage = FarmerConstants.ValidationMessages.PhoneInvalidFormat;
            return false;
        }

        return true;
    }
}
