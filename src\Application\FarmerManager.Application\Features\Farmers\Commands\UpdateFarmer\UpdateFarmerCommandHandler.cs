using FarmerManager.Application.Common.Models;
using FarmerManager.Application.Contracts.Repositories;
using FarmerManager.Application.DTOs.Farmer;
using FarmerManager.Application.Services.Interfaces;
using MediatR;

namespace FarmerManager.Application.Features.Farmers.Commands.UpdateFarmer;

public class UpdateFarmerCommandHandler : IRequestHandler<UpdateFarmerCommand, ApiResponse<FarmerDto>>
{
    private readonly IFarmerService _farmerService;

    public UpdateFarmerCommandHandler(IFarmerRepository farmerRepository, IFarmerService farmerService)
    {
        _farmerService = farmerService;
    }

    public async Task<ApiResponse<FarmerDto>> Handle(UpdateFarmerCommand request, CancellationToken cancellationToken)
    {
        return await _farmerService.UpdateFarmerAsync(request.UpdateFarmerDto, cancellationToken);
    }
}
