using FarmerManager.Application.Common.Models;
using FarmerManager.Application.Contracts.Repositories;
using MediatR;

namespace FarmerManager.Application.Features.Farmers.Commands.DeleteFarmer;

public class DeleteFarmerCommandHandler : IRequestHandler<DeleteFarmerCommand, ApiResponse<bool>>
{
    private readonly IFarmerRepository _farmerRepository;

    public DeleteFarmerCommandHandler(IFarmerRepository farmerRepository)
    {
        _farmerRepository = farmerRepository;
    }

    public async Task<ApiResponse<bool>> Handle(DeleteFarmerCommand request, CancellationToken cancellationToken)
    {
        // <PERSON><PERSON><PERSON> tra farmer có tồn tại không
        var existingFarmer = await _farmerRepository.GetByIdAsync(request.Id, cancellationToken);
        if (existingFarmer == null)
        {
            return ApiResponse<bool>.FailureResult("Không tìm thấy nông dân");
        }

        // Xóa farmer
        var result = await _farmerRepository.DeleteAsync(request.Id, cancellationToken);

        if (result)
        {
            return ApiResponse<bool>.SuccessResult(true, "Xóa nông dân thành công");
        }

        return ApiResponse<bool>.FailureResult("Không thể xóa nông dân");
    }
}
