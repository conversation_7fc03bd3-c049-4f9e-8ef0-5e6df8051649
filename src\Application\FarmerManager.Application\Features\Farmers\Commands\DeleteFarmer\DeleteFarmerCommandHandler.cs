using FarmerManager.Application.Common.Models;
using FarmerManager.Application.Contracts.Repositories;
using FarmerManager.Application.Services.Interfaces;
using MediatR;

namespace FarmerManager.Application.Features.Farmers.Commands.DeleteFarmer;

public class DeleteFarmerCommandHandler : IRequestHandler<DeleteFarmerCommand, ApiResponse<bool>>
{
    private readonly IFarmerService _farmerService;

    public DeleteFarmerCommandHandler(IFarmerRepository farmerRepository, IFarmerService farmerService)
    {
        _farmerService = farmerService;
    }

    public async Task<ApiResponse<bool>> Handle(DeleteFarmerCommand request, CancellationToken cancellationToken)
    {
        return await _farmerService.DeleteFarmerAsync(request.Id, cancellationToken);
    }
}
