﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="FluentValidation" Version="12.0.0" />
      <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="12.0.0" />
      <PackageReference Include="MediatR" Version="13.0.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\FarmerManager.Domain\FarmerManager.Domain.csproj" />
    </ItemGroup>

</Project>
