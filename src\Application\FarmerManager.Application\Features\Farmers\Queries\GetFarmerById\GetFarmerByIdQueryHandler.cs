using FarmerManager.Application.Common.Models;
using FarmerManager.Application.Contracts.Repositories;
using FarmerManager.Application.DTOs.Farmer;
using FarmerManager.Application.Services.Interfaces;
using MediatR;

namespace FarmerManager.Application.Features.Farmers.Queries.GetFarmerById;

public class GetFarmerByIdQueryHandler : IRequestHandler<GetFarmerByIdQuery, ApiResponse<FarmerDto>>
{
    private readonly IFarmerService _farmerService;

    public GetFarmerByIdQueryHandler(IFarmerRepository farmerRepository, IFarmerService farmerService)
    {
        _farmerService = farmerService;
    }

    public async Task<ApiResponse<FarmerDto>> Handle(GetFarmerByIdQuery request, CancellationToken cancellationToken)
    {
        return await _farmerService.GetFarmerByIdAsync(request.Id, cancellationToken);
    }
}