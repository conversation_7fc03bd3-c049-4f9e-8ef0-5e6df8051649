using FarmerManager.Application.Common.Models;
using FarmerManager.Application.Contracts.Repositories;
using FarmerManager.Application.DTOs.Farmer;
using MediatR;

namespace FarmerManager.Application.Features.Farmers.Queries.GetFarmerById;

public class GetFarmerByIdQueryHandler : IRequestHandler<GetFarmerByIdQuery, ApiResponse<FarmerDto>>
{
    private readonly IFarmerRepository _farmerRepository;

    public GetFarmerByIdQueryHandler(IFarmerRepository farmerRepository)
    {
        _farmerRepository = farmerRepository;
    }

    public async Task<ApiResponse<FarmerDto>> Handle(GetFarmerByIdQuery request, CancellationToken cancellationToken)
    {
        var farmer = await _farmerRepository.GetByIdAsync(request.Id, cancellationToken);

        if (farmer == null)
        {
            return ApiResponse<FarmerDto>.FailureResult("Không tìm thấy nông dân");
        }

        var farmerDto = new FarmerDto
        {
            Id = farmer.Id,
            FarmerCode = farmer.FarmerCode,
            FarmerName = farmer.FarmerName,
            FarmerNameEN = farmer.FarmerNameEN,
            Address = farmer.Address,
            Phone1 = farmer.Phone1,
            Phone2 = farmer.Phone2,
            InsertDate = farmer.InsertDate,
            UpdatedDate = farmer.UpdatedDate
        };

        return ApiResponse<FarmerDto>.SuccessResult(farmerDto, "Lấy thông tin nông dân thành công");
    }
}
