import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, But<PERSON>, Form, Modal, Spinner } from "react-bootstrap";
import { farmerService } from "../services/farmerService";
import { CreateFarmerDto, FarmerDto, UpdateFarmerDto } from "../types/farmer";

interface FarmerFormProps {
  show: boolean;
  onHide: () => void;
  onSuccess: () => void;
  farmer?: FarmerDto;
  mode: "create" | "edit";
}

const FarmerForm: React.FC<FarmerFormProps> = ({
  show,
  onHide,
  onSuccess,
  farmer,
  mode,
}) => {
  const [formData, setFormData] = useState({
    farmerCode: "",
    farmerName: "",
    farmerNameEN: "",
    address: "",
    phone1: "",
    phone2: "",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>("");
  const [errors, setErrors] = useState<string[]>([]);

  useEffect(() => {
    if (show) {
      if (mode === "edit" && farmer) {
        setFormData({
          farmerCode: farmer.farmerCode,
          farmerName: farmer.farmerName,
          farmerNameEN: farmer.farmerNameEN || "",
          address: farmer.address || "",
          phone1: farmer.phone1 || "",
          phone2: farmer.phone2 || "",
        });
      } else {
        setFormData({
          farmerCode: "",
          farmerName: "",
          farmerNameEN: "",
          address: "",
          phone1: "",
          phone2: "",
        });
      }
      setError("");
      setErrors([]);
    }
  }, [show, mode, farmer]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const validateForm = (): boolean => {
    const newErrors: string[] = [];

    if (!formData.farmerCode.trim()) {
      newErrors.push("Mã nông dân không được để trống");
    } else if (formData.farmerCode.length > 50) {
      newErrors.push("Mã nông dân không được vượt quá 50 ký tự");
    } else if (!/^[A-Za-z0-9_-]+$/.test(formData.farmerCode)) {
      newErrors.push(
        "Mã nông dân chỉ được chứa chữ cái, số, dấu gạch dưới và dấu gạch ngang"
      );
    }

    if (!formData.farmerName.trim()) {
      newErrors.push("Tên nông dân không được để trống");
    } else if (formData.farmerName.length > 200) {
      newErrors.push("Tên nông dân không được vượt quá 200 ký tự");
    }

    if (formData.farmerNameEN && formData.farmerNameEN.length > 200) {
      newErrors.push("Tên tiếng Anh không được vượt quá 200 ký tự");
    }

    if (formData.address && formData.address.length > 500) {
      newErrors.push("Địa chỉ không được vượt quá 500 ký tự");
    }

    if (formData.phone1 && formData.phone1.length > 20) {
      newErrors.push("Số điện thoại 1 không được vượt quá 20 ký tự");
    }

    if (formData.phone2 && formData.phone2.length > 20) {
      newErrors.push("Số điện thoại 2 không được vượt quá 20 ký tự");
    }

    setErrors(newErrors);
    return newErrors.length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError("");

    try {
      if (mode === "create") {
        const createData: CreateFarmerDto = {
          farmerCode: formData.farmerCode.trim(),
          farmerName: formData.farmerName.trim(),
          farmerNameEN: formData.farmerNameEN.trim() || undefined,
          address: formData.address.trim() || undefined,
          phone1: formData.phone1.trim() || undefined,
          phone2: formData.phone2.trim() || undefined,
        };

        const response = await farmerService.createFarmer(createData);
        if (response.success) {
          onSuccess();
          onHide();
        } else {
          setError(response.message);
          if (response.errors && response.errors.length > 0) {
            setErrors(response.errors);
          }
        }
      } else if (mode === "edit" && farmer) {
        const updateData: UpdateFarmerDto = {
          id: farmer.id,
          farmerCode: formData.farmerCode.trim(),
          farmerName: formData.farmerName.trim(),
          farmerNameEN: formData.farmerNameEN.trim() || undefined,
          address: formData.address.trim() || undefined,
          phone1: formData.phone1.trim() || undefined,
          phone2: formData.phone2.trim() || undefined,
        };

        const response = await farmerService.updateFarmer(
          farmer.id,
          updateData
        );
        if (response.success) {
          onSuccess();
          onHide();
        } else {
          setError(response.message);
          if (response.errors && response.errors.length > 0) {
            setErrors(response.errors);
          }
        }
      }
    } catch (err: any) {
      setError(err.response?.data?.message || "Có lỗi xảy ra");
      if (err.response?.data?.errors) {
        setErrors(err.response.data.errors);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal show={show} onHide={onHide} size="lg">
      <Modal.Header closeButton>
        <Modal.Title>
          {mode === "create" ? "Thêm mới nông dân" : "Chỉnh sửa nông dân"}
        </Modal.Title>
      </Modal.Header>

      <Form onSubmit={handleSubmit}>
        <Modal.Body>
          {error && <Alert variant="danger">{error}</Alert>}

          {errors.length > 0 && (
            <Alert variant="danger">
              <ul className="mb-0">
                {errors.map((err, index) => (
                  <li key={index}>{err}</li>
                ))}
              </ul>
            </Alert>
          )}
          {mode === "edit" && farmer && (
            <div className="row">
              <div className="col-md-6">
                <Form.Group className="mb-3">
                  <Form.Label>Ngày tạo</Form.Label>
                  <Form.Control
                    type="text"
                    value={new Date(farmer.insertDate).toLocaleString("vi-VN")}
                    disabled
                    readOnly
                    className="bg-light"
                  />
                </Form.Group>
              </div>

              {farmer.updatedDate && (
                <div className="col-md-6">
                  <Form.Group className="mb-3">
                    <Form.Label>Ngày cập nhật cuối</Form.Label>
                    <Form.Control
                      type="text"
                      value={new Date(farmer.updatedDate).toLocaleString(
                        "vi-VN"
                      )}
                      disabled
                      readOnly
                      className="bg-light"
                    />
                  </Form.Group>
                </div>
              )}
            </div>
          )}

          <div className="row">
            <div className="col-md-6">
              <Form.Group className="mb-3">
                <Form.Label>
                  Mã nông dân <span className="text-danger">*</span>
                </Form.Label>
                <Form.Control
                  type="text"
                  name="farmerCode"
                  value={formData.farmerCode}
                  onChange={handleInputChange}
                  placeholder="Nhập mã nông dân"
                  required
                  disabled={loading || mode === "edit"}
                  readOnly={mode === "edit"}
                  className={mode === "edit" ? "bg-light" : ""}
                />
                {mode === "edit" && (
                  <Form.Text className="text-muted">
                    <i className="bi bi-lock me-1"></i>
                    Mã nông dân không thể thay đổi
                  </Form.Text>
                )}
              </Form.Group>
            </div>

            <div className="col-md-6">
              <Form.Group className="mb-3">
                <Form.Label>
                  Tên nông dân <span className="text-danger">*</span>
                </Form.Label>
                <Form.Control
                  type="text"
                  name="farmerName"
                  value={formData.farmerName}
                  onChange={handleInputChange}
                  placeholder="Nhập tên nông dân"
                  required
                  disabled={loading}
                />
              </Form.Group>
            </div>
          </div>

          <div className="row">
            <div className="col-md-6">
              <Form.Group className="mb-3">
                <Form.Label>Tên tiếng Anh</Form.Label>
                <Form.Control
                  type="text"
                  name="farmerNameEN"
                  value={formData.farmerNameEN}
                  onChange={handleInputChange}
                  placeholder="Nhập tên tiếng Anh"
                  disabled={loading}
                />
              </Form.Group>
            </div>

            <div className="col-md-6">
              <Form.Group className="mb-3">
                <Form.Label>Địa chỉ</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={2}
                  name="address"
                  value={formData.address}
                  onChange={handleInputChange}
                  placeholder="Nhập địa chỉ"
                  disabled={loading}
                />
              </Form.Group>
            </div>
          </div>

          <div className="row">
            <div className="col-md-6">
              <Form.Group className="mb-3">
                <Form.Label>Số điện thoại 1</Form.Label>
                <Form.Control
                  type="text"
                  name="phone1"
                  value={formData.phone1}
                  onChange={handleInputChange}
                  placeholder="Nhập số điện thoại 1"
                  disabled={loading}
                />
              </Form.Group>
            </div>

            <div className="col-md-6">
              <Form.Group className="mb-3">
                <Form.Label>Số điện thoại 2</Form.Label>
                <Form.Control
                  type="text"
                  name="phone2"
                  value={formData.phone2}
                  onChange={handleInputChange}
                  placeholder="Nhập số điện thoại 2"
                  disabled={loading}
                />
              </Form.Group>
            </div>
          </div>
        </Modal.Body>

        <Modal.Footer>
          <Button variant="secondary" onClick={onHide} disabled={loading}>
            Hủy
          </Button>
          <Button variant="primary" type="submit" disabled={loading}>
            {loading && (
              <Spinner animation="border" size="sm" className="me-2" />
            )}
            {mode === "create" ? "Thêm mới" : "Cập nhật"}
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
};

export default FarmerForm;
