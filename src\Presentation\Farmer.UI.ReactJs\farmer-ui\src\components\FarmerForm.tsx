import { <PERSON><PERSON>, <PERSON><PERSON>, Col, Form, Input, Modal, Row } from "antd";
import React, { useEffect, useState } from "react";
import { farmerService } from "../services/farmerService";
import { CreateFarmerDto, FarmerDto, UpdateFarmerDto } from "../types/farmer";

interface FarmerFormProps {
  show: boolean;
  onHide: () => void;
  onSuccess: () => void;
  farmer?: FarmerDto;
  mode: "create" | "edit";
}

const FarmerForm: React.FC<FarmerFormProps> = ({
  show,
  onHide,
  onSuccess,
  farmer,
  mode,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>("");

  useEffect(() => {
    if (show) {
      if (mode === "edit" && farmer) {
        form.setFieldsValue({
          farmerCode: farmer.farmerCode,
          farmerName: farmer.farmerName,
          farmerNameEN: farmer.farmerNameEN || "",
          address: farmer.address || "",
          phone1: farmer.phone1 || "",
          phone2: farmer.phone2 || "",
        });
      } else {
        form.resetFields();
      }
      setError("");
    }
  }, [show, mode, farmer, form]);

  const handleSubmit = async (values: any) => {
    setLoading(true);
    setError("");

    try {
      if (mode === "create") {
        const createData: CreateFarmerDto = {
          farmerCode: values.farmerCode.trim(),
          farmerName: values.farmerName.trim(),
          farmerNameEN: values.farmerNameEN?.trim() || undefined,
          address: values.address?.trim() || undefined,
          phone1: values.phone1?.trim() || undefined,
          phone2: values.phone2?.trim() || undefined,
        };

        const response = await farmerService.createFarmer(createData);
        if (response.success) {
          onSuccess();
          onHide();
        } else {
          setError(response.message);
        }
      } else if (mode === "edit" && farmer) {
        const updateData: UpdateFarmerDto = {
          id: farmer.id,
          farmerCode: values.farmerCode.trim(),
          farmerName: values.farmerName.trim(),
          farmerNameEN: values.farmerNameEN?.trim() || undefined,
          address: values.address?.trim() || undefined,
          phone1: values.phone1?.trim() || undefined,
          phone2: values.phone2?.trim() || undefined,
        };

        const response = await farmerService.updateFarmer(
          farmer.id,
          updateData
        );
        if (response.success) {
          onSuccess();
          onHide();
        } else {
          setError(response.message);
        }
      }
    } catch (err: any) {
      setError(err.response?.data?.message || "Có lỗi xảy ra");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={mode === "create" ? "Thêm mới nông dân" : "Chỉnh sửa nông dân"}
      open={show}
      onCancel={onHide}
      footer={null}
      width={800}
      confirmLoading={loading}
    >
      {error && (
        <Alert
          message={error}
          type="error"
          closable
          style={{ marginBottom: 16 }}
        />
      )}

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        disabled={loading}
      >
        {mode === "edit" && farmer && (
          <Row gutter={16} style={{ marginBottom: 16 }}>
            <Col span={12}>
              <Form.Item label="Ngày tạo">
                <Input
                  value={new Date(farmer.insertDate).toLocaleString("vi-VN")}
                  disabled
                  style={{ backgroundColor: "#f5f5f5" }}
                />
              </Form.Item>
            </Col>
            {farmer.updatedDate && (
              <Col span={12}>
                <Form.Item label="Ngày cập nhật cuối">
                  <Input
                    value={new Date(farmer.updatedDate).toLocaleString("vi-VN")}
                    disabled
                    style={{ backgroundColor: "#f5f5f5" }}
                  />
                </Form.Item>
              </Col>
            )}
          </Row>
        )}

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Mã nông dân"
              name="farmerCode"
              rules={[
                { required: true, message: "Mã nông dân không được để trống" },
                {
                  max: 50,
                  message: "Mã nông dân không được vượt quá 50 ký tự",
                },
                {
                  pattern: /^[A-Za-z0-9_-]+$/,
                  message:
                    "Mã nông dân chỉ được chứa chữ cái, số, dấu gạch dưới và dấu gạch ngang",
                },
              ]}
            >
              <Input
                placeholder="Nhập mã nông dân"
                disabled={mode === "edit"}
                style={mode === "edit" ? { backgroundColor: "#f5f5f5" } : {}}
              />
            </Form.Item>
            {mode === "edit" && (
              <div
                style={{
                  color: "#666",
                  fontSize: "12px",
                  marginTop: "-16px",
                  marginBottom: "16px",
                }}
              >
                🔒 Mã nông dân không thể thay đổi
              </div>
            )}
          </Col>
          <Col span={12}>
            <Form.Item
              label="Tên nông dân"
              name="farmerName"
              rules={[
                { required: true, message: "Tên nông dân không được để trống" },
                {
                  max: 200,
                  message: "Tên nông dân không được vượt quá 200 ký tự",
                },
              ]}
            >
              <Input placeholder="Nhập tên nông dân" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Tên tiếng Anh"
              name="farmerNameEN"
              rules={[
                {
                  max: 200,
                  message: "Tên tiếng Anh không được vượt quá 200 ký tự",
                },
              ]}
            >
              <Input placeholder="Nhập tên tiếng Anh" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Địa chỉ"
              name="address"
              rules={[
                { max: 500, message: "Địa chỉ không được vượt quá 500 ký tự" },
              ]}
            >
              <Input.TextArea rows={2} placeholder="Nhập địa chỉ" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Số điện thoại 1"
              name="phone1"
              rules={[
                {
                  max: 20,
                  message: "Số điện thoại 1 không được vượt quá 20 ký tự",
                },
              ]}
            >
              <Input placeholder="Nhập số điện thoại 1" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Số điện thoại 2"
              name="phone2"
              rules={[
                {
                  max: 20,
                  message: "Số điện thoại 2 không được vượt quá 20 ký tự",
                },
              ]}
            >
              <Input placeholder="Nhập số điện thoại 2" />
            </Form.Item>
          </Col>
        </Row>

        <Row justify="end" style={{ marginTop: 24 }}>
          <Col>
            <Button onClick={onHide} style={{ marginRight: 8 }}>
              Hủy
            </Button>
            <Button type="primary" htmlType="submit" loading={loading}>
              {mode === "create" ? "Thêm mới" : "Cập nhật"}
            </Button>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default FarmerForm;
