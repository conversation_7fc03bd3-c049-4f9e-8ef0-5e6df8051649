<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.17" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.8">
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
          <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\Application\FarmerManager.Application\FarmerManager.Application.csproj" />
      <ProjectReference Include="..\..\Infrastructure\FarmerManager.Persistence\FarmerManager.Persistence.csproj" />
    </ItemGroup>

</Project>
