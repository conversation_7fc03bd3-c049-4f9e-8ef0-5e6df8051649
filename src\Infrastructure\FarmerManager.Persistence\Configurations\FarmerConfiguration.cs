using FarmerManager.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace FarmerManager.Persistence.Configurations;

public class FarmerConfiguration : IEntityTypeConfiguration<Farmer>
{
    public void Configure(EntityTypeBuilder<Farmer> builder)
    {
        builder.ToTable("Farmers");

        builder.HasKey(f => f.Id);

        builder.Property(f => f.Id)
            .HasColumnName("FarmerID")
            .ValueGeneratedOnAdd();

        // Configure RowId to not use Identity since Id is already using it
        builder.Property(f => f.RowId)
            .ValueGeneratedNever();

        builder.Property(f => f.FarmerCode)
            .IsRequired()
            .HasMaxLength(50)
            .HasColumnName("FarmerCode");

        builder.Property(f => f.FarmerName)
            .IsRequired()
            .HasMaxLength(200)
            .HasColumnName("FarmerName");

        builder.Property(f => f.FarmerNameEN)
            .HasMaxLength(200)
            .HasColumnName("FarmerNameEN");

        builder.Property(f => f.Address)
            .HasMaxLength(500)
            .HasColumnName("Address");

        builder.Property(f => f.Phone1)
            .HasMaxLength(20)
            .HasColumnName("Phone1");

        builder.Property(f => f.Phone2)
            .HasMaxLength(20)
            .HasColumnName("Phone2");

        builder.Property(f => f.InsertDate)
            .IsRequired()
            .HasColumnName("InsertDate")
            .HasColumnType("datetime");

        builder.Property(f => f.UpdatedDate)
            .HasColumnName("UpdatedDate")
            .HasColumnType("datetime");

        // Index for unique FarmerCode
        builder.HasIndex(f => f.FarmerCode)
            .IsUnique()
            .HasDatabaseName("IX_Farmers_FarmerCode");

        // Index for search
        builder.HasIndex(f => f.FarmerName)
            .HasDatabaseName("IX_Farmers_FarmerName");
    }
}
