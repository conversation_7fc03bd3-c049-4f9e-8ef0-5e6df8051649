using System.ComponentModel.DataAnnotations;

namespace FarmerManager.Application.DTOs.Farmer;

public class UpdateFarmerDto
{
    [Required(ErrorMessage = "ID không được để trống")]
    public int Id { get; set; }

    [Required(ErrorMessage = "Mã nông dân không được để trống")]
    [StringLength(50, ErrorMessage = "Mã nông dân không được vượt quá 50 ký tự")]
    public string FarmerCode { get; set; } = string.Empty;

    [Required(ErrorMessage = "Tên nông dân không được để trống")]
    [StringLength(200, ErrorMessage = "Tên nông dân không được vượt quá 200 ký tự")]
    public string FarmerName { get; set; } = string.Empty;

    [StringLength(200, ErrorMessage = "Tên tiếng Anh không được vượt quá 200 ký tự")]
    public string? FarmerNameEN { get; set; }

    [StringLength(500, ErrorMessage = "Địa chỉ không được vượt quá 500 ký tự")]
    public string? Address { get; set; }

    [StringLength(20, ErrorMessage = "Số điện thoại 1 không được vượt quá 20 ký tự")]
    public string? Phone1 { get; set; }

    [StringLength(20, ErrorMessage = "Số điện thoại 2 không được vượt quá 20 ký tự")]
    public string? Phone2 { get; set; }
}
