namespace FarmerManager.Constants;

public static class FarmerConstants
{
    public static class ValidationMessages
    {
        public const string FarmerCodeRequired = "Mã nông dân không được để trống";
        public const string FarmerCodeMaxLength = "Mã nông dân không được vượt quá {0} ký tự";
        public const string FarmerCodeInvalidFormat = "Mã nông dân chỉ được chứa chữ cái, số, dấu gạch dưới và dấu gạch ngang";
        public const string FarmerCodeExists = "Mã nông dân đã tồn tại";
        
        public const string FarmerNameRequired = "Tên nông dân không được để trống";
        public const string FarmerNameMaxLength = "Tên nông dân không được vượt quá {0} ký tự";
        
        public const string FarmerNameENMaxLength = "Tên tiếng Anh không được vượt quá {0} ký tự";
        public const string AddressMaxLength = "Địa chỉ không được vượt quá {0} ký tự";
        
        public const string PhoneMaxLength = "Số điện thoại không được vượt quá {0} ký tự";
        public const string PhoneInvalidFormat = "Số điện thoại không đúng định dạng";
        
        public const string IdRequired = "ID không được để trống";
        public const string IdMustBeGreaterThanZero = "ID phải lớn hơn 0";
    }

    public static class SuccessMessages
    {
        public const string FarmerCreated = "Tạo nông dân thành công";
        public const string FarmerUpdated = "Cập nhật nông dân thành công";
        public const string FarmerDeleted = "Xóa nông dân thành công";
        public const string FarmerRetrieved = "Lấy thông tin nông dân thành công";
        public const string FarmersRetrieved = "Lấy danh sách nông dân thành công";
    }

    public static class ErrorMessages
    {
        public const string FarmerNotFound = "Không tìm thấy nông dân";
        public const string FarmerDeleteFailed = "Không thể xóa nông dân";
        public const string GeneralError = "Có lỗi xảy ra";
    }

    public static class FieldLengths
    {
        public const int FarmerCodeMaxLength = 50;
        public const int FarmerNameMaxLength = 200;
        public const int FarmerNameENMaxLength = 200;
        public const int AddressMaxLength = 500;
        public const int PhoneMaxLength = 20;
    }

    public static class RegexPatterns
    {
        public const string FarmerCodePattern = @"^[A-Za-z0-9_-]+$";
        public const string PhonePattern = @"^[\d\s\-\+\(\)]+$";
    }

    public static class DefaultValues
    {
        public const int DefaultPageSize = 10;
        public const int DefaultPageNumber = 1;
        public const int MaxPageSize = 100;
    }
}
