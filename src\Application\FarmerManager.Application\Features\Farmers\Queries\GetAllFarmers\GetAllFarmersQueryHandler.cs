using FarmerManager.Application.Common.Models;
using FarmerManager.Application.Contracts.Repositories;
using FarmerManager.Application.DTOs.Farmer;
using FarmerManager.Application.Services.Interfaces;
using MediatR;

namespace FarmerManager.Application.Features.Farmers.Queries.GetAllFarmers;

public class GetAllFarmersQueryHandler : IRequestHandler<GetAllFarmersQuery, ApiResponse<PagedResult<FarmerDto>>>
{
    private readonly IFarmerService _farmerService;

    public GetAllFarmersQueryHandler(IFarmerRepository farmerRepository, IFarmerService farmerService)
    {
        _farmerService = farmerService;
    }

    public async Task<ApiResponse<PagedResult<FarmerDto>>> Handle(GetAllFarmersQuery request, CancellationToken cancellationToken)
    {
        return await _farmerService.GetAllFarmersAsync(request.SearchDto, cancellationToken);
    }
}