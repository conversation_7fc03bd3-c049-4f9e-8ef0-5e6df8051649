using FarmerManager.Application.Common.Models;
using FarmerManager.Application.Contracts.Repositories;
using FarmerManager.Application.DTOs.Farmer;
using MediatR;

namespace FarmerManager.Application.Features.Farmers.Queries.GetAllFarmers;

public class GetAllFarmersQueryHandler : IRequestHandler<GetAllFarmersQuery, ApiResponse<PagedResult<FarmerDto>>>
{
    private readonly IFarmerRepository _farmerRepository;

    public GetAllFarmersQueryHandler(IFarmerRepository farmerRepository)
    {
        _farmerRepository = farmerRepository;
    }

    public async Task<ApiResponse<PagedResult<FarmerDto>>> Handle(GetAllFarmersQuery request, CancellationToken cancellationToken)
    {
        var farmers = await _farmerRepository.GetAllAsync(request.SearchDto, cancellationToken);

        var farmerDtos = farmers.Items.Select(f => new FarmerDto
        {
            Id = f.Id,
            FarmerCode = f.FarmerCode,
            FarmerName = f.FarmerName,
            FarmerNameEN = f.<PERSON>,
            Address = f.Address,
            Phone1 = f.Phone1,
            Phone2 = f.Phone2,
            InsertDate = f.InsertDate,
            UpdatedDate = f.UpdatedDate
        }).ToList();

        var result = new PagedResult<FarmerDto>(farmerDtos, farmers.TotalCount, farmers.PageNumber, farmers.PageSize);

        return ApiResponse<PagedResult<FarmerDto>>.SuccessResult(result, "Lấy danh sách nông dân thành công");
    }
}
