using FarmerManager.Application.DTOs.Farmer;
using FarmerManager.Application.Contracts.Repositories;
using FluentValidation;

namespace FarmerManager.Application.Validators;

public class CreateFarmerDtoValidator : AbstractValidator<CreateFarmerDto>
{
    private readonly IFarmerRepository _farmerRepository;

    public CreateFarmerDtoValidator(IFarmerRepository farmerRepository)
    {
        _farmerRepository = farmerRepository;
        RuleFor(x => x.FarmerCode)
            .NotEmpty().WithMessage("Mã nông dân không được để trống")
            .MaximumLength(50).WithMessage("Mã nông dân không được vượt quá 50 ký tự")
            .Matches("^[A-Za-z0-9_-]+$").WithMessage("Mã nông dân chỉ được chứa chữ cái, số, dấu gạch dưới và dấu gạch ngang")
            .MustAsync(async (farmerCode, cancellation) =>
            {
                var exists = await _farmerRepository.ExistsByCodeAsync(farmerCode, cancellation);
                return !exists;
            }).WithMessage("Mã nông dân đã tồn tại trong hệ thống");

        RuleFor(x => x.FarmerName)
            .NotEmpty().WithMessage("Tên nông dân không được để trống")
            .MaximumLength(200).WithMessage("Tên nông dân không được vượt quá 200 ký tự");

        RuleFor(x => x.FarmerNameEN)
            .MaximumLength(200).WithMessage("Tên tiếng Anh không được vượt quá 200 ký tự")
            .When(x => !string.IsNullOrEmpty(x.FarmerNameEN));

        RuleFor(x => x.Address)
            .MaximumLength(500).WithMessage("Địa chỉ không được vượt quá 500 ký tự")
            .When(x => !string.IsNullOrEmpty(x.Address));

        RuleFor(x => x.Phone1)
            .MaximumLength(20).WithMessage("Số điện thoại 1 không được vượt quá 20 ký tự")
            .Matches(@"^[\d\s\-\+\(\)]+$").WithMessage("Số điện thoại 1 không đúng định dạng")
            .When(x => !string.IsNullOrEmpty(x.Phone1));

        RuleFor(x => x.Phone2)
            .MaximumLength(20).WithMessage("Số điện thoại 2 không được vượt quá 20 ký tự")
            .Matches(@"^[\d\s\-\+\(\)]+$").WithMessage("Số điện thoại 2 không đúng định dạng")
            .When(x => !string.IsNullOrEmpty(x.Phone2));
    }
}
