using FarmerManager.Application.Common.Models;
using FarmerManager.Persistence.Services;
using Microsoft.AspNetCore.Mvc;

namespace Farmer.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class DataController : ControllerBase
{
    private readonly IDataInitializationService _dataInitService;
    private readonly ILogger<DataController> _logger;

    public DataController(IDataInitializationService dataInitService, ILogger<DataController> logger)
    {
        _dataInitService = dataInitService;
        _logger = logger;
    }

    /// <summary>
    /// Khởi tạo dữ liệu mẫu (chỉ dành cho development)
    /// </summary>
    [HttpPost("init")]
    public async Task<ActionResult<ApiResponse<string>>> InitializeData()
    {
        try
        {
            await _dataInitService.InitializeAsync();
            _logger.LogInformation("Data initialization completed successfully");
            
            return Ok(ApiResponse<string>.SuccessResult("Khởi tạo dữ liệu thành công", "Data initialized successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during data initialization");
            return BadRequest(ApiResponse<string>.FailureResult("Có lỗi xảy ra khi khởi tạo dữ liệu"));
        }
    }
}
